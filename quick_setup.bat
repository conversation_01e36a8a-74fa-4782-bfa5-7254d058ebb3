@echo off
chcp 65001 >nul
echo ================================================================
echo 基于CNN的猫狗图像分类项目 - 快速环境配置
echo ================================================================
echo.

echo 1. 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.7+
    pause
    exit /b 1
)

echo.
echo 2. 升级pip...
python -m pip install --upgrade pip

echo.
echo 3. 安装基础依赖库...
echo 正在安装numpy...
pip install numpy

echo 正在安装matplotlib...
pip install matplotlib

echo 正在安装scikit-learn...
pip install scikit-learn

echo 正在安装seaborn...
pip install seaborn

echo.
echo 4. 安装可选依赖库...
echo 正在安装opencv-python...
pip install opencv-python

echo 正在安装tensorflow...
pip install tensorflow

echo.
echo 5. 运行简化测试...
python simple_test.py

echo.
echo 6. 检查安装结果...
python -c "
import sys
packages = ['numpy', 'matplotlib', 'sklearn', 'seaborn']
optional = ['cv2', 'tensorflow']

print('基础依赖检查:')
for pkg in packages:
    try:
        __import__(pkg)
        print(f'✓ {pkg}')
    except ImportError:
        print(f'✗ {pkg}')

print('\n可选依赖检查:')
for pkg in optional:
    try:
        __import__(pkg)
        print(f'✓ {pkg}')
    except ImportError:
        print(f'✗ {pkg}')
"

echo.
echo ================================================================
echo 环境配置完成！
echo ================================================================
echo.
echo 下一步操作:
echo 1. 如果所有依赖都安装成功，可以运行完整项目
echo 2. 下载Kaggle猫狗数据集
echo 3. 运行训练脚本: python cnn-classification-dog-vs-cat-master/train.py
echo.
pause
