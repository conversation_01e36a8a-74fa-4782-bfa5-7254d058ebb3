#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据集自动组织脚本
将Kaggle下载的猫狗图像数据自动组织成训练所需的目录结构
"""

import os
import shutil
import glob
import random
from pathlib import Path

def organize_dataset(source_dir, target_dir, train_ratio=0.95):
    """
    自动组织数据集
    
    参数:
        source_dir: 解压后的原始数据目录
        target_dir: 目标组织目录
        train_ratio: 训练集比例，默认0.95 (95%训练，5%验证)
    """
    
    print("=" * 60)
    print("猫狗图像数据集自动组织工具")
    print("=" * 60)
    
    # 检查源目录是否存在
    if not os.path.exists(source_dir):
        print(f"❌ 源目录不存在: {source_dir}")
        print("请确保已下载并解压Kaggle的train.zip文件")
        return False
    
    # 创建目标目录结构
    train_cat_dir = os.path.join(target_dir, 'train', 'cat')
    train_dog_dir = os.path.join(target_dir, 'train', 'dog')
    dev_cat_dir = os.path.join(target_dir, 'dev', 'cat')
    dev_dog_dir = os.path.join(target_dir, 'dev', 'dog')
    
    print(f"正在创建目录结构...")
    for dir_path in [train_cat_dir, train_dog_dir, dev_cat_dir, dev_dog_dir]:
        os.makedirs(dir_path, exist_ok=True)
        print(f"✓ {dir_path}")
    
    # 获取所有图像文件
    print(f"\n正在扫描源目录: {source_dir}")
    cat_files = glob.glob(os.path.join(source_dir, 'cat.*.jpg'))
    dog_files = glob.glob(os.path.join(source_dir, 'dog.*.jpg'))
    
    print(f"找到猫图像: {len(cat_files)}张")
    print(f"找到狗图像: {len(dog_files)}张")
    
    if len(cat_files) == 0 or len(dog_files) == 0:
        print("❌ 未找到图像文件，请检查源目录路径")
        return False
    
    # 随机打乱文件列表以确保随机分配
    random.shuffle(cat_files)
    random.shuffle(dog_files)
    
    # 计算训练集和验证集的数量
    cat_train_count = int(len(cat_files) * train_ratio)
    dog_train_count = int(len(dog_files) * train_ratio)
    
    print(f"\n数据分配:")
    print(f"训练集 - 猫: {cat_train_count}张, 狗: {dog_train_count}张")
    print(f"验证集 - 猫: {len(cat_files)-cat_train_count}张, 狗: {len(dog_files)-dog_train_count}张")
    
    # 复制猫图像
    print(f"\n正在复制猫图像...")
    for i, file_path in enumerate(cat_files):
        filename = os.path.basename(file_path)
        if i < cat_train_count:
            target_path = os.path.join(train_cat_dir, filename)
        else:
            target_path = os.path.join(dev_cat_dir, filename)
        
        shutil.copy2(file_path, target_path)
        
        # 显示进度
        if (i + 1) % 1000 == 0:
            print(f"  已处理: {i + 1}/{len(cat_files)}")
    
    print(f"✓ 猫图像复制完成: {len(cat_files)}张")
    
    # 复制狗图像
    print(f"\n正在复制狗图像...")
    for i, file_path in enumerate(dog_files):
        filename = os.path.basename(file_path)
        if i < dog_train_count:
            target_path = os.path.join(train_dog_dir, filename)
        else:
            target_path = os.path.join(dev_dog_dir, filename)
        
        shutil.copy2(file_path, target_path)
        
        # 显示进度
        if (i + 1) % 1000 == 0:
            print(f"  已处理: {i + 1}/{len(dog_files)}")
    
    print(f"✓ 狗图像复制完成: {len(dog_files)}张")
    
    # 验证结果
    print(f"\n验证数据组织结果:")
    train_cat_count = len(os.listdir(train_cat_dir))
    train_dog_count = len(os.listdir(train_dog_dir))
    dev_cat_count = len(os.listdir(dev_cat_dir))
    dev_dog_count = len(os.listdir(dev_dog_dir))
    
    print(f"训练集 - 猫: {train_cat_count}张, 狗: {train_dog_count}张")
    print(f"验证集 - 猫: {dev_cat_count}张, 狗: {dev_dog_count}张")
    print(f"总计: {train_cat_count + train_dog_count + dev_cat_count + dev_dog_count}张")
    
    print(f"\n🎉 数据组织完成！")
    print(f"目标目录: {target_dir}")
    
    return True

def check_dataset_structure(target_dir):
    """检查数据集结构是否正确"""
    print("\n" + "=" * 60)
    print("数据集结构检查")
    print("=" * 60)
    
    required_dirs = [
        'train/cat',
        'train/dog', 
        'dev/cat',
        'dev/dog'
    ]
    
    all_exist = True
    for dir_name in required_dirs:
        dir_path = os.path.join(target_dir, dir_name)
        if os.path.exists(dir_path):
            file_count = len(os.listdir(dir_path))
            print(f"✓ {dir_name}: {file_count}个文件")
        else:
            print(f"❌ {dir_name}: 目录不存在")
            all_exist = False
    
    if all_exist:
        print("\n✅ 数据集结构检查通过！")
        return True
    else:
        print("\n❌ 数据集结构不完整")
        return False

def main():
    """主函数"""
    print("基于CNN的猫狗图像分类 - 数据集组织工具")
    
    # 默认路径配置
    default_source = "./train"  # Kaggle解压后的目录
    default_target = "./cnn-classification-dog-vs-cat-master/inputs"
    
    # 获取用户输入或使用默认路径
    print(f"\n请输入数据路径（直接回车使用默认值）:")
    source_dir = input(f"源目录 [{default_source}]: ").strip()
    if not source_dir:
        source_dir = default_source
    
    target_dir = input(f"目标目录 [{default_target}]: ").strip()
    if not target_dir:
        target_dir = default_target
    
    print(f"\n配置信息:")
    print(f"源目录: {source_dir}")
    print(f"目标目录: {target_dir}")
    
    # 确认执行
    confirm = input(f"\n是否开始组织数据集? (y/n): ").lower().strip()
    if confirm not in ['y', 'yes', '是']:
        print("操作已取消")
        return
    
    # 执行数据组织
    success = organize_dataset(source_dir, target_dir)
    
    if success:
        # 检查结果
        check_dataset_structure(target_dir)
        
        print(f"\n🚀 下一步操作:")
        print(f"1. 验证环境: python verify_environment.py")
        print(f"2. 开始训练:")
        print(f"   - 自定义CNN: cd cnn-classification-dog-vs-cat-master && python train.py")
        print(f"   - VGG16迁移学习: cd cnn-classification-dog-vs-cat-master && python pre_train.py")
    else:
        print(f"\n❌ 数据组织失败，请检查路径和文件")

if __name__ == "__main__":
    # 设置随机种子以确保可重现性
    random.seed(42)
    main()
