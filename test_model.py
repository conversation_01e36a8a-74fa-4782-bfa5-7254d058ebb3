#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于CNN的猫狗图像分类 - 模型测试脚本
用于验证模型的基本功能和性能
"""

import os
import sys
import numpy as np
import cv2
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix, classification_report
import seaborn as sns

# 添加项目路径
sys.path.append('./cnn-classification-dog-vs-cat-master')

try:
    import data_helper
    from img_cnn import ImgCNN
    print("✓ 成功导入项目模块")
except ImportError as e:
    print(f"✗ 导入模块失败: {e}")
    print("请确保项目文件在正确的目录中")

def test_data_helper():
    """测试数据处理模块"""
    print("\n=== 测试数据处理模块 ===")
    
    # 测试图像调整功能
    try:
        # 创建测试图像
        test_img = np.random.randint(0, 255, (300, 400, 3), dtype=np.uint8)
        cv2.imwrite('test_image.jpg', test_img)
        
        # 测试图像调整
        resized_img = data_helper.img_resize('test_image.jpg', 224, 224)
        print(f"✓ 图像调整功能正常，输出尺寸: {resized_img.shape}")
        
        # 测试RGB转灰度
        gray_img = data_helper.rgb2gray(resized_img)
        print(f"✓ 灰度转换功能正常，输出尺寸: {gray_img.shape}")
        
        # 清理测试文件
        os.remove('test_image.jpg')
        
    except Exception as e:
        print(f"✗ 数据处理模块测试失败: {e}")

def test_model_architecture():
    """测试模型架构"""
    print("\n=== 测试模型架构 ===")
    
    try:
        import tensorflow as tf
        
        # 重置默认图
        tf.reset_default_graph()
        
        # 创建模型实例
        with tf.Session() as sess:
            cnn = ImgCNN(
                n_classes=2,
                img_height=224,
                img_width=224,
                img_channel=1,
                device_name='/cpu:0'
            )
            
            # 初始化变量
            sess.run(tf.global_variables_initializer())
            
            # 测试前向传播
            test_input = np.random.random((1, 224, 224, 1)).astype(np.float32)
            test_label = np.array([[1, 0]]).astype(np.float32)
            
            feed_dict = {
                cnn.input_x: test_input,
                cnn.input_y: test_label,
                cnn.dropout_keep_prob: 1.0
            }
            
            output, loss, accuracy = sess.run(
                [cnn.output, cnn.loss, cnn.accuracy], 
                feed_dict
            )
            
            print(f"✓ 模型架构测试通过")
            print(f"  - 输出形状: {output.shape}")
            print(f"  - 损失值: {loss:.4f}")
            print(f"  - 准确率: {accuracy:.4f}")
            
    except Exception as e:
        print(f"✗ 模型架构测试失败: {e}")

def create_sample_data():
    """创建示例数据用于测试"""
    print("\n=== 创建示例数据 ===")
    
    # 创建目录结构
    os.makedirs('./test_data/train/cat', exist_ok=True)
    os.makedirs('./test_data/train/dog', exist_ok=True)
    
    # 生成示例图像
    for i in range(10):
        # 猫的图像（偏蓝色）
        cat_img = np.random.randint(0, 100, (224, 224, 3), dtype=np.uint8)
        cat_img[:, :, 2] = np.random.randint(150, 255, (224, 224))  # 增强蓝色通道
        cv2.imwrite(f'./test_data/train/cat/cat.{i}.jpg', cat_img)
        
        # 狗的图像（偏红色）
        dog_img = np.random.randint(0, 100, (224, 224, 3), dtype=np.uint8)
        dog_img[:, :, 0] = np.random.randint(150, 255, (224, 224))  # 增强红色通道
        cv2.imwrite(f'./test_data/train/dog/dog.{i}.jpg', dog_img)
    
    print("✓ 示例数据创建完成")
    print("  - 猫图像: 10张")
    print("  - 狗图像: 10张")

def test_data_loading():
    """测试数据加载功能"""
    print("\n=== 测试数据加载 ===")
    
    try:
        # 测试文件路径和标签获取
        img_paths, labels = data_helper.get_filenames_and_labels('./test_data/train/')
        
        print(f"✓ 数据加载功能正常")
        print(f"  - 图像数量: {len(img_paths)}")
        print(f"  - 标签形状: {labels.shape}")
        print(f"  - 猫标签示例: {labels[0]}")
        print(f"  - 狗标签示例: {labels[-1]}")
        
        # 测试批处理迭代器
        batch_count = 0
        for x_batch, y_batch in data_helper.batch_iter(
            batch_size=4, 
            num_epochs=1, 
            img_path_list=img_paths[:8], 
            label_list=labels[:8],
            img_height=224, 
            img_width=224
        ):
            batch_count += 1
            if batch_count == 1:
                print(f"✓ 批处理功能正常")
                print(f"  - 批次图像形状: {x_batch.shape}")
                print(f"  - 批次标签形状: {y_batch.shape}")
            if batch_count >= 2:  # 只测试前两个批次
                break
                
    except Exception as e:
        print(f"✗ 数据加载测试失败: {e}")

def visualize_sample_data():
    """可视化示例数据"""
    print("\n=== 可视化示例数据 ===")
    
    try:
        # 加载示例图像
        cat_img = cv2.imread('./test_data/train/cat/cat.0.jpg')
        dog_img = cv2.imread('./test_data/train/dog/dog.0.jpg')
        
        # 转换颜色空间（OpenCV使用BGR，matplotlib使用RGB）
        cat_img_rgb = cv2.cvtColor(cat_img, cv2.COLOR_BGR2RGB)
        dog_img_rgb = cv2.cvtColor(dog_img, cv2.COLOR_BGR2RGB)
        
        # 创建图表
        fig, axes = plt.subplots(1, 2, figsize=(10, 4))
        
        axes[0].imshow(cat_img_rgb)
        axes[0].set_title('示例猫图像')
        axes[0].axis('off')
        
        axes[1].imshow(dog_img_rgb)
        axes[1].set_title('示例狗图像')
        axes[1].axis('off')
        
        plt.tight_layout()
        plt.savefig('sample_data_visualization.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✓ 数据可视化完成，保存为 sample_data_visualization.png")
        
    except Exception as e:
        print(f"✗ 数据可视化失败: {e}")

def test_preprocessing_pipeline():
    """测试完整的预处理流程"""
    print("\n=== 测试预处理流程 ===")
    
    try:
        # 加载图像
        img_path = './test_data/train/cat/cat.0.jpg'
        
        # 步骤1: 调整尺寸
        resized_img = data_helper.img_resize(img_path, 224, 224)
        print(f"✓ 步骤1 - 尺寸调整: {resized_img.shape}")
        
        # 步骤2: 转换为灰度
        gray_img = data_helper.rgb2gray(resized_img)
        print(f"✓ 步骤2 - 灰度转换: {gray_img.shape}")
        
        # 步骤3: 检查像素值范围
        print(f"✓ 步骤3 - 像素值范围: [{gray_img.min():.3f}, {gray_img.max():.3f}]")
        
        # 可视化预处理结果
        fig, axes = plt.subplots(1, 2, figsize=(10, 4))
        
        # 原始图像
        original_img = cv2.cvtColor(resized_img, cv2.COLOR_BGR2RGB)
        axes[0].imshow(original_img)
        axes[0].set_title('原始图像')
        axes[0].axis('off')
        
        # 灰度图像
        axes[1].imshow(gray_img.squeeze(), cmap='gray')
        axes[1].set_title('灰度图像')
        axes[1].axis('off')
        
        plt.tight_layout()
        plt.savefig('preprocessing_pipeline.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✓ 预处理流程测试完成，结果保存为 preprocessing_pipeline.png")
        
    except Exception as e:
        print(f"✗ 预处理流程测试失败: {e}")

def cleanup_test_data():
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    
    try:
        import shutil
        if os.path.exists('./test_data'):
            shutil.rmtree('./test_data')
            print("✓ 测试数据清理完成")
    except Exception as e:
        print(f"✗ 清理测试数据失败: {e}")

def main():
    """主测试函数"""
    print("=" * 50)
    print("基于CNN的猫狗图像分类 - 模型测试")
    print("=" * 50)
    
    # 检查依赖库
    print("\n=== 检查依赖库 ===")
    required_packages = ['numpy', 'opencv-python', 'matplotlib', 'sklearn', 'seaborn']
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - 请安装此依赖库")
    
    # 运行测试
    test_data_helper()
    create_sample_data()
    test_data_loading()
    visualize_sample_data()
    test_preprocessing_pipeline()
    test_model_architecture()
    
    # 清理
    cleanup_test_data()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)
    
    print("\n下一步操作建议：")
    print("1. 下载Kaggle猫狗数据集")
    print("2. 按照指南组织数据目录结构")
    print("3. 运行 python train.py 训练自定义CNN模型")
    print("4. 运行 python pre_train.py 训练VGG16迁移学习模型")
    print("5. 比较两种模型的性能差异")

if __name__ == "__main__":
    main()
