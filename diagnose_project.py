#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
项目诊断脚本
检查现有项目的完整性和可运行性
"""

import os
import sys
import importlib

def check_python_environment():
    """检查Python环境"""
    print("=" * 50)
    print("Python环境检查")
    print("=" * 50)
    
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    # 检查必要的库
    required_packages = [
        'numpy', 'matplotlib', 'cv2', 'sklearn', 
        'h5py', 'gflags', 'tensorflow', 'keras'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            module = importlib.import_module(package)
            if hasattr(module, '__version__'):
                print(f"✅ {package}: {module.__version__}")
            else:
                print(f"✅ {package}: 已安装")
        except ImportError:
            print(f"❌ {package}: 未安装")
            missing_packages.append(package)
    
    return len(missing_packages) == 0, missing_packages

def check_project_files():
    """检查项目文件"""
    print("\n" + "=" * 50)
    print("项目文件检查")
    print("=" * 50)
    
    project_dir = "cnn-classification-dog-vs-cat-master"
    
    if not os.path.exists(project_dir):
        print(f"❌ 项目目录不存在: {project_dir}")
        return False
    
    # 检查核心文件
    core_files = {
        "train.py": "自定义CNN训练脚本",
        "pre_train.py": "VGG16迁移学习脚本", 
        "data_helper.py": "数据处理模块",
        "img_cnn.py": "CNN模型定义"
    }
    
    all_files_exist = True
    
    for filename, description in core_files.items():
        filepath = os.path.join(project_dir, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            print(f"✅ {filename}: {description} ({size} bytes)")
        else:
            print(f"❌ {filename}: {description} - 文件不存在")
            all_files_exist = False
    
    return all_files_exist

def check_data_structure():
    """检查数据结构"""
    print("\n" + "=" * 50)
    print("数据结构检查")
    print("=" * 50)
    
    project_dir = "cnn-classification-dog-vs-cat-master"
    inputs_dir = os.path.join(project_dir, "inputs")
    
    if not os.path.exists(inputs_dir):
        print(f"❌ 数据目录不存在: {inputs_dir}")
        return False
    
    # 检查训练数据
    train_dir = os.path.join(inputs_dir, "train")
    if os.path.exists(train_dir):
        print(f"✅ 训练目录存在: {train_dir}")
        
        # 检查类别目录
        cat_dir = os.path.join(train_dir, "cat")
        dog_dir = os.path.join(train_dir, "dog")
        
        cat_count = 0
        dog_count = 0
        
        if os.path.exists(cat_dir):
            cat_files = [f for f in os.listdir(cat_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            cat_count = len(cat_files)
            print(f"✅ 猫图像: {cat_count}张")
        else:
            print(f"❌ 猫目录不存在: {cat_dir}")
        
        if os.path.exists(dog_dir):
            dog_files = [f for f in os.listdir(dog_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            dog_count = len(dog_files)
            print(f"✅ 狗图像: {dog_count}张")
        else:
            print(f"❌ 狗目录不存在: {dog_dir}")
        
        total_images = cat_count + dog_count
        print(f"总图像数量: {total_images}")
        
        if total_images > 0:
            return True
        else:
            print("❌ 没有找到训练图像")
            return False
    else:
        print(f"❌ 训练目录不存在: {train_dir}")
        return False

def check_code_syntax():
    """检查代码语法"""
    print("\n" + "=" * 50)
    print("代码语法检查")
    print("=" * 50)
    
    project_dir = "cnn-classification-dog-vs-cat-master"
    python_files = ["train.py", "pre_train.py", "data_helper.py", "img_cnn.py"]
    
    all_syntax_ok = True
    
    for filename in python_files:
        filepath = os.path.join(project_dir, filename)
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    code = f.read()
                compile(code, filepath, 'exec')
                print(f"✅ {filename}: 语法正确")
            except SyntaxError as e:
                print(f"❌ {filename}: 语法错误 - {e}")
                all_syntax_ok = False
            except Exception as e:
                print(f"⚠️ {filename}: 检查时出错 - {e}")
        else:
            print(f"❌ {filename}: 文件不存在")
            all_syntax_ok = False
    
    return all_syntax_ok

def generate_run_commands():
    """生成运行命令"""
    print("\n" + "=" * 50)
    print("推荐运行命令")
    print("=" * 50)
    
    print("如果所有检查都通过，可以尝试以下命令：")
    print()
    print("# 进入项目目录")
    print("cd cnn-classification-dog-vs-cat-master")
    print()
    print("# 运行自定义CNN训练")
    print("python train.py")
    print()
    print("# 或运行VGG16迁移学习")
    print("python pre_train.py")
    print()
    print("# 如果遇到错误，请将错误信息发给我")

def main():
    """主诊断函数"""
    print("CNN猫狗分类项目 - 完整诊断")
    
    # 检查Python环境
    env_ok, missing_packages = check_python_environment()
    
    # 检查项目文件
    files_ok = check_project_files()
    
    # 检查数据结构
    data_ok = check_data_structure()
    
    # 检查代码语法
    syntax_ok = check_code_syntax()
    
    # 生成总结
    print("\n" + "=" * 50)
    print("诊断总结")
    print("=" * 50)
    
    issues = []
    
    if not env_ok:
        issues.append(f"缺少依赖库: {', '.join(missing_packages)}")
    
    if not files_ok:
        issues.append("项目文件不完整")
    
    if not data_ok:
        issues.append("数据集不完整或不存在")
    
    if not syntax_ok:
        issues.append("代码语法有错误")
    
    if len(issues) == 0:
        print("🎉 所有检查都通过！项目可以运行")
        generate_run_commands()
    else:
        print("❌ 发现以下问题:")
        for i, issue in enumerate(issues, 1):
            print(f"{i}. {issue}")
        
        print("\n解决建议:")
        if not env_ok:
            print("- 运行: python quick_install.py")
        if not files_ok:
            print("- 检查项目文件是否完整")
        if not data_ok:
            print("- 检查数据集是否正确放置")
        if not syntax_ok:
            print("- 检查代码文件是否损坏")

if __name__ == "__main__":
    main()
