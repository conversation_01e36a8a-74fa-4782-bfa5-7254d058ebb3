#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动安装依赖包脚本
解决网络连接问题，分步安装所需的Python包
"""

import subprocess
import sys
import time

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n{'='*60}")
    print(f"正在{description}...")
    print(f"命令: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {description}成功")
            if result.stdout:
                print("输出:", result.stdout[-200:])  # 只显示最后200个字符
            return True
        else:
            print(f"❌ {description}失败")
            print("错误:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description}超时")
        return False
    except Exception as e:
        print(f"❌ {description}出现异常: {e}")
        return False

def install_with_multiple_sources(package, description):
    """尝试多个源安装包"""
    
    # 方法1: 使用trusted-host
    cmd1 = f"pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org {package}"
    
    # 方法2: 使用清华镜像
    cmd2 = f"pip install -i https://pypi.tuna.tsinghua.edu.cn/simple {package}"
    
    # 方法3: 使用阿里镜像
    cmd3 = f"pip install -i https://mirrors.aliyun.com/pypi/simple/ {package}"
    
    # 方法4: 使用豆瓣镜像
    cmd4 = f"pip install -i https://pypi.douban.com/simple/ {package}"
    
    methods = [
        (cmd1, f"使用trusted-host安装{description}"),
        (cmd2, f"使用清华镜像安装{description}"),
        (cmd3, f"使用阿里镜像安装{description}"),
        (cmd4, f"使用豆瓣镜像安装{description}")
    ]
    
    for cmd, desc in methods:
        print(f"\n尝试: {desc}")
        if run_command(cmd, desc):
            return True
        time.sleep(2)  # 等待2秒后尝试下一个方法
    
    print(f"❌ 所有方法都失败了，无法安装 {package}")
    return False

def main():
    """主安装函数"""
    print("基于CNN的猫狗图像分类项目 - 依赖包自动安装")
    print("=" * 60)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major != 3 or python_version.minor < 7:
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    
    # 升级pip
    print("\n首先升级pip...")
    pip_upgrade_success = install_with_multiple_sources("--upgrade pip", "pip升级")
    
    # 定义要安装的包
    packages = [
        ("numpy==1.24.3", "NumPy数值计算库"),
        ("matplotlib==3.7.2", "Matplotlib绘图库"),
        ("opencv-python==********", "OpenCV计算机视觉库"),
        ("scikit-learn==1.3.0", "Scikit-learn机器学习库"),
        ("seaborn==0.12.2", "Seaborn统计绘图库"),
        ("h5py==3.9.0", "HDF5文件处理库"),
        ("python-gflags==3.1.2", "命令行参数解析库"),
        ("tensorflow==2.13.0", "TensorFlow深度学习框架"),
        ("keras==2.13.1", "Keras高级神经网络API")
    ]
    
    # 安装统计
    success_count = 0
    total_count = len(packages)
    
    # 逐个安装包
    for package, description in packages:
        print(f"\n{'='*60}")
        print(f"安装进度: {success_count}/{total_count}")
        print(f"{'='*60}")
        
        if install_with_multiple_sources(package, description):
            success_count += 1
        else:
            print(f"⚠️ {description}安装失败，但继续安装其他包...")
    
    # 安装结果总结
    print(f"\n{'='*60}")
    print("安装结果总结")
    print(f"{'='*60}")
    print(f"成功安装: {success_count}/{total_count} 个包")
    
    if success_count == total_count:
        print("🎉 所有包安装成功！")
        print("\n下一步操作:")
        print("1. 运行环境验证: python verify_environment.py")
        print("2. 准备数据集")
        print("3. 开始训练")
        return True
    elif success_count >= total_count - 2:  # 允许1-2个包失败
        print("⚠️ 大部分包安装成功，可以尝试继续")
        print("\n建议:")
        print("1. 运行环境验证: python verify_environment.py")
        print("2. 如果验证失败，手动安装缺失的包")
        return True
    else:
        print("❌ 安装失败的包太多，需要解决网络问题")
        print("\n建议:")
        print("1. 检查网络连接")
        print("2. 配置代理设置（如果在公司网络）")
        print("3. 尝试使用conda安装")
        return False

def show_manual_install_commands():
    """显示手动安装命令"""
    print(f"\n{'='*60}")
    print("手动安装命令（如果自动安装失败）")
    print(f"{'='*60}")
    
    commands = [
        "# 方法1: 使用trusted-host",
        "pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org tensorflow==2.13.0",
        "",
        "# 方法2: 使用清华镜像",
        "pip install -i https://pypi.tuna.tsinghua.edu.cn/simple tensorflow==2.13.0",
        "",
        "# 方法3: 使用conda（如果有）",
        "conda install tensorflow=2.13.0 -c conda-forge",
        "",
        "# 其他包类似安装",
        "pip install -i https://pypi.tuna.tsinghua.edu.cn/simple keras opencv-python numpy matplotlib scikit-learn seaborn h5py python-gflags"
    ]
    
    for cmd in commands:
        print(cmd)

if __name__ == "__main__":
    try:
        success = main()
        
        if not success:
            show_manual_install_commands()
            
    except KeyboardInterrupt:
        print("\n\n安装已中断")
    except Exception as e:
        print(f"\n安装过程中出现错误: {e}")
        show_manual_install_commands()
