# 数据集准备指南

## 📁 数据集下载

### 1. 从Kaggle下载数据集

1. **访问Kaggle竞赛页面**：
   - 网址：https://www.kaggle.com/c/dogs-vs-cats/data
   - 需要注册Kaggle账号

2. **下载文件**：
   - 下载 `train.zip` 文件（约543MB）
   - 包含25,000张训练图像

3. **解压文件**：
   ```bash
   # 解压到当前目录
   unzip train.zip
   ```

### 2. 组织数据目录结构

解压后您会得到一个包含所有图像的文件夹。需要按以下结构重新组织：

```
cnn-classification-dog-vs-cat-master/
└── inputs/
    ├── train/
    │   ├── cat/
    │   │   ├── cat.0.jpg
    │   │   ├── cat.1.jpg
    │   │   ├── ...
    │   │   └── cat.12499.jpg
    │   └── dog/
    │       ├── dog.0.jpg
    │       ├── dog.1.jpg
    │       ├── ...
    │       └── dog.12499.jpg
    └── dev/
        ├── cat/
        │   ├── cat.12000.jpg
        │   ├── cat.12001.jpg
        │   └── ...
        └── dog/
            ├── dog.12000.jpg
            ├── dog.12001.jpg
            └── ...
```

### 3. 自动数据组织脚本

我为您创建一个自动组织数据的脚本：

```python
import os
import shutil
import glob

def organize_dataset(source_dir, target_dir):
    """
    自动组织数据集
    
    参数:
        source_dir: 解压后的原始数据目录
        target_dir: 目标组织目录
    """
    
    # 创建目标目录结构
    train_cat_dir = os.path.join(target_dir, 'train', 'cat')
    train_dog_dir = os.path.join(target_dir, 'train', 'dog')
    dev_cat_dir = os.path.join(target_dir, 'dev', 'cat')
    dev_dog_dir = os.path.join(target_dir, 'dev', 'dog')
    
    for dir_path in [train_cat_dir, train_dog_dir, dev_cat_dir, dev_dog_dir]:
        os.makedirs(dir_path, exist_ok=True)
    
    # 获取所有图像文件
    cat_files = glob.glob(os.path.join(source_dir, 'cat.*.jpg'))
    dog_files = glob.glob(os.path.join(source_dir, 'dog.*.jpg'))
    
    print(f"找到猫图像: {len(cat_files)}张")
    print(f"找到狗图像: {len(dog_files)}张")
    
    # 分配训练集和验证集 (95%训练，5%验证)
    cat_train_count = int(len(cat_files) * 0.95)
    dog_train_count = int(len(dog_files) * 0.95)
    
    # 复制猫图像
    for i, file_path in enumerate(cat_files):
        filename = os.path.basename(file_path)
        if i < cat_train_count:
            shutil.copy2(file_path, os.path.join(train_cat_dir, filename))
        else:
            shutil.copy2(file_path, os.path.join(dev_cat_dir, filename))
    
    # 复制狗图像
    for i, file_path in enumerate(dog_files):
        filename = os.path.basename(file_path)
        if i < dog_train_count:
            shutil.copy2(file_path, os.path.join(train_dog_dir, filename))
        else:
            shutil.copy2(file_path, os.path.join(dev_dog_dir, filename))
    
    print("数据组织完成！")
    print(f"训练集 - 猫: {cat_train_count}张, 狗: {dog_train_count}张")
    print(f"验证集 - 猫: {len(cat_files)-cat_train_count}张, 狗: {len(dog_files)-dog_train_count}张")

# 使用示例
if __name__ == "__main__":
    source_directory = "./train"  # 解压后的原始目录
    target_directory = "./cnn-classification-dog-vs-cat-master/inputs"
    
    organize_dataset(source_directory, target_directory)
```

### 4. 快速数据准备步骤

```bash
# 1. 下载并解压数据
# (手动从Kaggle下载train.zip并解压)

# 2. 运行数据组织脚本
python organize_dataset.py

# 3. 验证数据结构
ls -la cnn-classification-dog-vs-cat-master/inputs/train/cat/ | head -5
ls -la cnn-classification-dog-vs-cat-master/inputs/train/dog/ | head -5
```

### 5. 数据集统计信息

组织完成后的数据集信息：
- **总图像数量**: 25,000张
- **训练集**: 约23,750张 (95%)
  - 猫: 约11,875张
  - 狗: 约11,875张
- **验证集**: 约1,250张 (5%)
  - 猫: 约625张
  - 狗: 约625张

### 6. 验证数据准备

运行以下命令验证数据是否正确准备：

```bash
# 检查目录结构
find cnn-classification-dog-vs-cat-master/inputs -type d

# 统计文件数量
echo "训练集猫图像数量:"
ls cnn-classification-dog-vs-cat-master/inputs/train/cat/ | wc -l

echo "训练集狗图像数量:"
ls cnn-classification-dog-vs-cat-master/inputs/train/dog/ | wc -l

echo "验证集猫图像数量:"
ls cnn-classification-dog-vs-cat-master/inputs/dev/cat/ | wc -l

echo "验证集狗图像数量:"
ls cnn-classification-dog-vs-cat-master/inputs/dev/dog/ | wc -l
```

## ⚠️ 注意事项

1. **存储空间**: 确保有足够的磁盘空间（至少2GB）
2. **文件权限**: 确保对目标目录有写权限
3. **文件完整性**: 检查下载的文件是否完整
4. **路径正确**: 确保脚本中的路径与实际情况匹配

## 🚀 完成数据准备后

数据准备完成后，您就可以运行训练脚本了：

```bash
# 进入项目目录
cd cnn-classification-dog-vs-cat-master

# 运行自定义CNN训练
python train.py

# 或运行VGG16迁移学习
python pre_train.py
```

## 📊 预期训练时间

- **自定义CNN**: 2-4小时 (CPU), 30-60分钟 (GPU)
- **VGG16迁移学习**: 1-2小时 (CPU), 15-30分钟 (GPU)

数据准备是成功训练的关键步骤，请确保按照指南正确组织数据结构！
