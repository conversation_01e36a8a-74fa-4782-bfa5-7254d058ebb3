#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速安装脚本 - 为现有项目安装依赖
"""

import subprocess
import sys
import os

def run_pip_install(packages, description):
    """使用多个镜像源尝试安装"""
    
    # 多个镜像源
    mirrors = [
        "https://pypi.tuna.tsinghua.edu.cn/simple",
        "https://mirrors.aliyun.com/pypi/simple/",
        "https://pypi.douban.com/simple/",
        "https://pypi.mirrors.ustc.edu.cn/simple/"
    ]
    
    print(f"\n正在安装 {description}...")
    
    for mirror in mirrors:
        try:
            cmd = f"pip install -i {mirror} {packages}"
            print(f"尝试镜像: {mirror}")
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=180)
            
            if result.returncode == 0:
                print(f"✅ {description} 安装成功")
                return True
            else:
                print(f"❌ 失败: {result.stderr[:100]}...")
                
        except subprocess.TimeoutExpired:
            print("⏰ 超时，尝试下一个镜像")
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print(f"❌ {description} 安装失败")
    return False

def check_project_structure():
    """检查项目结构"""
    print("检查项目结构...")
    
    project_dir = "cnn-classification-dog-vs-cat-master"
    
    if not os.path.exists(project_dir):
        print(f"❌ 项目目录不存在: {project_dir}")
        return False
    
    required_files = ["train.py", "pre_train.py", "data_helper.py", "img_cnn.py"]
    missing_files = []
    
    for file in required_files:
        file_path = os.path.join(project_dir, file)
        if os.path.exists(file_path):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    # 检查数据目录
    inputs_dir = os.path.join(project_dir, "inputs")
    if os.path.exists(inputs_dir):
        print(f"✅ inputs目录存在")
        
        # 检查数据子目录
        train_dir = os.path.join(inputs_dir, "train")
        if os.path.exists(train_dir):
            print(f"✅ train目录存在")
            
            # 检查类别目录
            cat_dir = os.path.join(train_dir, "cat")
            dog_dir = os.path.join(train_dir, "dog")
            
            if os.path.exists(cat_dir):
                cat_count = len([f for f in os.listdir(cat_dir) if f.endswith('.jpg')])
                print(f"✅ cat目录: {cat_count}张图片")
            else:
                print(f"❌ cat目录不存在")
            
            if os.path.exists(dog_dir):
                dog_count = len([f for f in os.listdir(dog_dir) if f.endswith('.jpg')])
                print(f"✅ dog目录: {dog_count}张图片")
            else:
                print(f"❌ dog目录不存在")
        else:
            print(f"❌ train目录不存在")
    else:
        print(f"❌ inputs目录不存在")
    
    return len(missing_files) == 0

def main():
    """主函数"""
    print("=" * 60)
    print("CNN猫狗分类项目 - 快速环境配置")
    print("=" * 60)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查项目结构
    if not check_project_structure():
        print("\n⚠️ 项目结构不完整，但继续安装依赖...")
    
    # 安装基础依赖
    packages_to_install = [
        ("numpy matplotlib", "基础科学计算库"),
        ("opencv-python", "OpenCV图像处理库"),
        ("scikit-learn", "机器学习库"),
        ("h5py", "HDF5文件处理库"),
        ("python-gflags", "命令行参数库"),
        ("tensorflow==2.13.0", "TensorFlow深度学习框架"),
        ("keras==2.13.1", "Keras神经网络库")
    ]
    
    success_count = 0
    
    for packages, description in packages_to_install:
        if run_pip_install(packages, description):
            success_count += 1
    
    print(f"\n{'='*60}")
    print("安装结果")
    print(f"{'='*60}")
    print(f"成功安装: {success_count}/{len(packages_to_install)} 个包")
    
    if success_count >= len(packages_to_install) - 1:  # 允许1个失败
        print("🎉 环境配置基本完成！")
        print("\n下一步操作:")
        print("1. cd cnn-classification-dog-vs-cat-master")
        print("2. python train.py  # 训练自定义CNN")
        print("3. python pre_train.py  # 训练VGG16迁移学习")
    else:
        print("❌ 安装失败较多，需要手动解决")

if __name__ == "__main__":
    main()
