# 基于CNN的猫狗图像分类项目 - 代码测试和运行指南

## 项目概述

本项目实现了基于卷积神经网络（CNN）的猫狗图像分类，包含两种不同的模型实现：
1. 自定义CNN模型（train.py）
2. 基于VGG16的迁移学习模型（pre_train.py）

## 环境配置

### 1. 依赖库安装

#### 方法一：使用 conda（推荐）
```bash
# 创建虚拟环境
conda create -n cat_dog_classification python=3.8
conda activate cat_dog_classification

# 安装主要依赖
conda install tensorflow-gpu=2.6.0  # 如果有GPU
# 或者
conda install tensorflow=2.6.0      # CPU版本

conda install keras=2.6.0
conda install opencv=4.5.1
conda install numpy=1.21.0
conda install matplotlib=3.4.2
conda install scikit-learn=0.24.2
conda install seaborn=0.11.1

# 安装其他依赖
pip install h5py==3.1.0
pip install python-gflags==3.1.2
```

#### 方法二：使用 pip
```bash
# 创建虚拟环境
python -m venv cat_dog_env
# Windows
cat_dog_env\Scripts\activate
# Linux/Mac
source cat_dog_env/bin/activate

# 安装依赖库
pip install tensorflow==2.6.0
pip install keras==2.6.0
pip install opencv-python==********
pip install numpy==1.21.0
pip install matplotlib==3.4.2
pip install scikit-learn==0.24.2
pip install seaborn==0.11.1
pip install h5py==3.1.0
pip install python-gflags==3.1.2
```

#### 方法三：最简安装（适合快速测试）
```bash
pip install tensorflow opencv-python matplotlib scikit-learn seaborn
```

### 2. 数据集准备

1. 从Kaggle下载Dogs vs. Cats数据集：
   - 访问：https://www.kaggle.com/c/dogs-vs-cats/data
   - 下载train.zip文件

2. 解压并组织数据结构：
```
cnn-classification-dog-vs-cat-master/
├── inputs/
│   ├── train/
│   │   ├── cat/
│   │   │   ├── cat.0.jpg
│   │   │   ├── cat.1.jpg
│   │   │   └── ...
│   │   └── dog/
│   │       ├── dog.0.jpg
│   │       ├── dog.1.jpg
│   │       └── ...
│   └── dev/
│       ├── cat/
│       └── dog/
├── train.py
├── pre_train.py
├── data_helper.py
└── img_cnn.py
```

## 代码文件说明

### 1. data_helper.py
数据预处理模块，包含以下功能：
- `get_filenames_and_labels()`: 获取文件路径和标签
- `img_resize()`: 图像尺寸调整
- `rgb2gray()`: RGB转灰度
- `batch_iter()`: 批处理迭代器

### 2. img_cnn.py
自定义CNN模型定义，包含：
- 3个卷积层 + 池化层
- 2个全连接层
- Dropout正则化

### 3. train.py
自定义CNN模型训练脚本：
- 使用TensorFlow 1.x实现
- 支持TensorBoard可视化
- 模型检查点保存

### 4. pre_train.py
VGG16迁移学习模型训练脚本：
- 使用Keras实现
- 基于预训练VGG16模型
- 数据增强支持

## 运行步骤

### 方法一：训练自定义CNN模型

```bash
cd cnn-classification-dog-vs-cat-master
python train.py
```

**参数说明**：
- `--train_data_dir`: 训练数据目录（默认：./inputs/train/）
- `--img_height`: 图像高度（默认：224）
- `--img_width`: 图像宽度（默认：224）
- `--batch_size`: 批次大小（默认：32）
- `--num_epochs`: 训练轮数（默认：200）
- `--learning_rate`: 学习率（默认：0.001）

### 方法二：训练VGG16迁移学习模型

```bash
cd cnn-classification-dog-vs-cat-master
python pre_train.py
```

**参数说明**：
- `--train_data_dir`: 训练数据目录（默认：./inputs/train/）
- `--dev_data_dir`: 验证数据目录（默认：./inputs/dev/）
- `--img_height`: 图像高度（默认：224）
- `--img_width`: 图像宽度（默认：224）
- `--batch_size`: 批次大小（默认：32）
- `--num_epochs`: 训练轮数（默认：50）

## 训练监控

### 1. TensorBoard可视化（自定义CNN）

```bash
# 启动TensorBoard
tensorboard --logdir=./log/

# 在浏览器中访问
http://localhost:6006
```

### 2. Keras训练历史（VGG16迁移学习）

训练过程中会自动显示损失和准确率曲线图。

## 预期结果

### 自定义CNN模型
- 训练时间：约2-3小时（CPU）
- 最终准确率：约83%
- 模型大小：约2MB

### VGG16迁移学习模型
- 训练时间：约1小时（GPU推荐）
- 最终准确率：约95%
- 模型大小：约60MB

## 常见问题解决

### 1. 内存不足
```python
# 在train.py中调整批次大小
gflags.DEFINE_integer('batch_size', 16, 'Reduce batch size')
```

### 2. CUDA相关错误
```bash
# 使用CPU版本
pip uninstall tensorflow-gpu
pip install tensorflow==1.15.0
```

### 3. 数据路径错误
确保数据目录结构正确，每个类别的图片放在对应的文件夹中。

## 模型评估

### 1. 准确率计算
模型训练过程中会自动计算并显示准确率。

### 2. 混淆矩阵
可以添加以下代码生成混淆矩阵：

```python
from sklearn.metrics import confusion_matrix, classification_report
import numpy as np

# 获取预测结果
y_pred = model.predict(x_test)
y_pred_classes = np.argmax(y_pred, axis=1)
y_true = np.argmax(y_test, axis=1)

# 生成混淆矩阵
cm = confusion_matrix(y_true, y_pred_classes)
print("Confusion Matrix:")
print(cm)

# 生成分类报告
report = classification_report(y_true, y_pred_classes,
                             target_names=['Cat', 'Dog'])
print("Classification Report:")
print(report)
```

## 模型保存和加载

### 1. 自定义CNN模型
```python
# 模型会自动保存在./log/目录下
# 加载模型
saver = tf.train.Saver()
saver.restore(sess, checkpoint_path)
```

### 2. VGG16迁移学习模型
```python
# 保存模型
model.save('./log/VGG16-transfer-learning.h5')

# 加载模型
from keras.models import load_model
model = load_model('./log/VGG16-transfer-learning.h5')
```

## 性能优化建议

### 1. 硬件优化
- 使用GPU加速训练（推荐GTX 1060以上）
- 增加内存容量（推荐16GB以上）

### 2. 软件优化
- 使用数据增强减少过拟合
- 调整学习率和批次大小
- 使用早停机制避免过训练

### 3. 数据优化
- 确保数据质量，移除损坏图片
- 平衡数据集，保证类别均衡
- 适当增加数据集规模

## 扩展功能

### 1. 实时预测
```python
import cv2
import numpy as np

def predict_single_image(image_path, model):
    # 加载和预处理图像
    img = cv2.imread(image_path)
    img = cv2.resize(img, (224, 224))
    img = img / 255.0
    img = np.expand_dims(img, axis=0)

    # 预测
    prediction = model.predict(img)
    class_name = 'Cat' if prediction[0][0] > 0.5 else 'Dog'
    confidence = max(prediction[0][0], 1 - prediction[0][0])

    return class_name, confidence
```

### 2. 批量测试
```python
def batch_predict(test_dir, model):
    results = []
    for filename in os.listdir(test_dir):
        if filename.endswith(('.jpg', '.png', '.jpeg')):
            image_path = os.path.join(test_dir, filename)
            class_name, confidence = predict_single_image(image_path, model)
            results.append({
                'filename': filename,
                'prediction': class_name,
                'confidence': confidence
            })
    return results
```

## 论文写作建议

1. **实验记录**：详细记录训练过程中的参数设置和结果
2. **结果可视化**：保存训练曲线、混淆矩阵等图表
3. **对比分析**：比较两种模型的性能差异
4. **错误分析**：分析模型预测错误的案例
5. **改进建议**：提出模型优化的方向

## 联系方式

如有问题，请参考：
- 项目GitHub：https://github.com/chenhui0518/cnn-classification-dog-vs-cat
- TensorFlow官方文档：https://tensorflow.org/
- Keras官方文档：https://keras.io/
