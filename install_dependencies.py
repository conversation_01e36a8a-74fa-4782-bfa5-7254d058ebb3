#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
依赖库安装脚本
自动检测并安装所需的依赖库
"""

import subprocess
import sys
import os

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装包"""
    print(f"正在安装 {package_name}...")
    success, stdout, stderr = run_command(f"pip install {package_name}")
    if success:
        print(f"✓ {package_name} 安装成功")
        return True
    else:
        print(f"✗ {package_name} 安装失败: {stderr}")
        return False

def main():
    """主安装函数"""
    print("=" * 60)
    print("基于CNN的猫狗图像分类项目 - 依赖库安装脚本")
    print("=" * 60)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print("⚠ 警告: 建议使用Python 3.7或更高版本")
    
    # 升级pip
    print("\n正在升级pip...")
    run_command("python -m pip install --upgrade pip")
    
    # 定义需要安装的包
    packages = {
        # 包名: (导入名, 描述)
        'numpy': ('numpy', '数值计算库'),
        'matplotlib': ('matplotlib', '绘图库'),
        'scikit-learn': ('sklearn', '机器学习库'),
        'seaborn': ('seaborn', '统计绘图库'),
        'opencv-python': ('cv2', '计算机视觉库'),
        'tensorflow': ('tensorflow', '深度学习框架'),
        'keras': ('keras', '高级神经网络API'),
        'h5py': ('h5py', 'HDF5文件处理'),
        'python-gflags': ('gflags', '命令行参数解析')
    }
    
    # 检查已安装的包
    print("\n检查已安装的包:")
    installed_packages = []
    missing_packages = []
    
    for package_name, (import_name, description) in packages.items():
        if check_package(import_name):
            print(f"✓ {package_name} ({description})")
            installed_packages.append(package_name)
        else:
            print(f"✗ {package_name} ({description}) - 需要安装")
            missing_packages.append(package_name)
    
    if not missing_packages:
        print("\n🎉 所有依赖库都已安装！")
        return
    
    # 询问是否安装缺失的包
    print(f"\n发现 {len(missing_packages)} 个缺失的包:")
    for package in missing_packages:
        print(f"  - {package}")
    
    response = input("\n是否要安装这些包? (y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("安装已取消")
        return
    
    # 安装缺失的包
    print("\n开始安装缺失的包...")
    success_count = 0
    
    for package_name in missing_packages:
        if install_package(package_name):
            success_count += 1
    
    # 安装结果总结
    print("\n" + "=" * 60)
    print("安装结果总结")
    print("=" * 60)
    print(f"成功安装: {success_count}/{len(missing_packages)} 个包")
    
    if success_count == len(missing_packages):
        print("🎉 所有包安装成功！")
    else:
        print("⚠ 部分包安装失败，请手动安装")
    
    # 再次检查安装结果
    print("\n最终检查:")
    all_installed = True
    for package_name, (import_name, description) in packages.items():
        if check_package(import_name):
            print(f"✓ {package_name}")
        else:
            print(f"✗ {package_name}")
            all_installed = False
    
    if all_installed:
        print("\n✅ 环境配置完成！可以运行项目代码了。")
        print("\n下一步操作:")
        print("1. 运行简化测试: python simple_test.py")
        print("2. 下载数据集并运行完整训练")
    else:
        print("\n❌ 仍有部分依赖未安装成功")
        print("\n手动安装命令:")
        for package_name, (import_name, description) in packages.items():
            if not check_package(import_name):
                print(f"pip install {package_name}")

def create_requirements_file():
    """创建requirements.txt文件"""
    requirements = [
        "numpy>=1.19.0",
        "matplotlib>=3.3.0", 
        "scikit-learn>=0.24.0",
        "seaborn>=0.11.0",
        "opencv-python>=4.5.0",
        "tensorflow>=2.6.0",
        "keras>=2.6.0",
        "h5py>=3.1.0",
        "python-gflags>=3.1.2"
    ]
    
    with open('requirements.txt', 'w', encoding='utf-8') as f:
        f.write("# 基于CNN的猫狗图像分类项目依赖\n")
        f.write("# 安装命令: pip install -r requirements.txt\n\n")
        for req in requirements:
            f.write(f"{req}\n")
    
    print("✓ 已创建 requirements.txt 文件")
    print("  可以使用 'pip install -r requirements.txt' 安装所有依赖")

if __name__ == "__main__":
    try:
        main()
        
        # 询问是否创建requirements.txt
        response = input("\n是否创建 requirements.txt 文件? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            create_requirements_file()
            
    except KeyboardInterrupt:
        print("\n\n安装已中断")
    except Exception as e:
        print(f"\n安装过程中出现错误: {e}")
        print("请手动安装依赖库")
