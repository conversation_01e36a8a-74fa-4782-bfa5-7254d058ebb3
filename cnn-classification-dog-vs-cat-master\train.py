# -*- coding: utf-8 -*-
"""
基于自定义CNN的猫狗图像分类 - 训练脚本
使用自定义的卷积神经网络进行猫狗图像分类
"""

import sys
import os
import time
import datetime
import gflags
import numpy as np
import tensorflow as tf
import data_helper
from img_cnn import ImgCNN


### 参数配置 ###
# ===============================================
FLAGS = gflags.FLAGS

# 数据加载参数
gflags.DEFINE_string('train_data_dir', './inputs/train/', '训练数据目录路径')
gflags.DEFINE_float('dev_sample_percentage', 0.01, '用于验证的训练数据百分比')

# 模型参数
gflags.DEFINE_integer('img_height', 224, '训练图像的高度 (默认: 224)')
gflags.DEFINE_integer('img_width', 224, '训练图像的宽度 (默认: 224)')
gflags.DEFINE_integer('img_channels', 1, '训练图像的通道数 (默认: 1，灰度图)')
gflags.DEFINE_float('dropout_keep_prob', 0.7, 'Dropout保留概率 (默认: 0.7)')

# 训练参数
gflags.DEFINE_float('learning_rate', 0.001, '学习率')
gflags.DEFINE_integer('batch_size', 32, '每个训练步骤的批次大小')
gflags.DEFINE_integer('num_epochs', 200, '训练轮数 (默认: 200)')
gflags.DEFINE_integer('evaluate_every', 100, '每多少步在验证集上评估模型 (默认: 100)')
gflags.DEFINE_integer('checkpoint_every', 100, '每多少步保存模型 (默认: 100)')
gflags.DEFINE_integer('num_checkpoints', 5, '保存的检查点数量 (默认: 5)')

# 设备参数
gflags.DEFINE_string('device_name', '/cpu:0', '训练设备名称')
gflags.DEFINE_bool('allow_soft_placement', True, '允许设备软放置')
gflags.DEFINE_bool('log_device_placement', False, '记录操作在设备上的放置')

FLAGS(sys.argv)
# 显示参数配置
print('\n参数配置:')
print('================================')
for attr, value in FLAGS.flag_values_dict().items():
    print('{0}: {1}'.format(attr.lower(), value))
print('================================\n\n')


### 数据准备 ###
# ===============================================

# 加载数据
print('正在加载数据...\n')
x_path, y = data_helper.get_filenames_and_labels(FLAGS.train_data_dir)

# 划分训练集/验证集
split_index = -int(float(len(y)) * FLAGS.dev_sample_percentage)
x_path_train, x_path_dev = x_path[:split_index], x_path[split_index:]
y_train, y_dev = y[:split_index], y[split_index:]

del x_path, y

# 预处理验证集数据
print('正在预处理验证集数据...')
x_dev = []
for i in range(len(x_path_dev)):
    # 调整图像尺寸
    img_data = data_helper.img_resize(img_path=x_path_dev[i], img_height=FLAGS.img_height, img_width=FLAGS.img_width)
    # 转换为灰度图像并归一化
    img_data = data_helper.rgb2gray(img_data)
    x_dev.append(img_data)
x_dev = np.array(x_dev)
y_dev = np.array(y_dev)

print(f'训练集大小: {len(x_path_train)}')
print(f'验证集大小: {len(x_dev)}')

input('按回车键开始训练...\n\n')
### 模型训练 ###
# ===============================================
print('开始训练模型...\n')
with tf.Graph().as_default():
    # 配置TensorFlow会话
    session_conf = tf.ConfigProto(
        allow_soft_placement=FLAGS.allow_soft_placement,
        log_device_placement=FLAGS.log_device_placement)
    sess = tf.Session(config=session_conf)
    with sess.as_default():
        # 创建CNN模型
        cnn = ImgCNN(
            n_classes=y_train.shape[1],      # 分类数量
            img_height=FLAGS.img_height,     # 图像高度
            img_width=FLAGS.img_width,       # 图像宽度
            img_channel=FLAGS.img_channels,  # 图像通道数
            device_name=FLAGS.device_name    # 设备名称
            )

        # 定义训练过程
        global_step = tf.Variable(0, trainable=False, name='global_step')
        optimizer = tf.train.AdamOptimizer(learning_rate=FLAGS.learning_rate)
        grads_and_vars = optimizer.compute_gradients(cnn.loss)
        train_op = optimizer.apply_gradients(grads_and_vars, global_step=global_step)

        # 模型和摘要的输出目录
        timestamp = str(int(time.time()))
        out_dir = os.path.abspath(os.path.join(os.curdir, 'log', timestamp))
        print('日志输出到: {}\n'.format(out_dir))

        # 摘要输入图像
        tf.summary.image('input_image', cnn.input_image, max_outputs=FLAGS.batch_size)

        # 摘要所有可训练变量
        for var in tf.trainable_variables():
            tf.summary.histogram(name=var.name, values=var)

        # 摘要损失和准确率
        loss_summary = tf.summary.scalar('loss', cnn.loss)
        acc_summary = tf.summary.scalar('accuracy', cnn.accuracy)

        # 训练摘要
        train_summary_op = tf.summary.merge_all()
        train_summary_dir = os.path.join(out_dir, 'summaries', 'train')
        train_summary_writer = tf.summary.FileWriter(train_summary_dir, tf.get_default_graph())

        # 验证摘要
        dev_summary_op = tf.summary.merge_all()
        dev_summary_dir = os.path.join(out_dir, 'summaries', 'dev')
        dev_summary_writer = tf.summary.FileWriter(dev_summary_dir, tf.get_default_graph())

        # 检查点保存，TensorFlow假设此目录已存在，所以我们需要创建它
        checkpoint_dir = os.path.join(out_dir, 'checkpoints')
        checkpoint_prefix = os.path.join(checkpoint_dir, 'model')
        if not os.path.exists(checkpoint_dir):
            os.makedirs(checkpoint_dir)
        saver = tf.train.Saver(tf.global_variables(), max_to_keep=FLAGS.num_checkpoints)

        def train_step(x_batch, y_batch, writer=None):
            """
            单次训练步骤
            """
            feed_dict = {
                cnn.input_x: x_batch,
                cnn.input_y: y_batch,
                cnn.dropout_keep_prob: FLAGS.dropout_keep_prob
            }
            _, step, summaries, loss, accuracy = sess.run(
                [train_op, global_step, train_summary_op, cnn.loss, cnn.accuracy],
                feed_dict)
            timestr = datetime.datetime.now().isoformat()
            print('{}: 步骤 {}, 损失 {:g}, 准确率 {:g}'.format(timestr, step, loss, accuracy))
            if writer:
                writer.add_summary(summaries, step)

        def dev_step(x_batch, y_batch, writer=None):
            """
            在验证集上评估模型
            """
            feed_dict = {
                cnn.input_x: x_batch,
                cnn.input_y: y_batch,
                cnn.dropout_keep_prob: 1.0
            }
            step, summaries, loss, accuracy = sess.run(
                [global_step, dev_summary_op, cnn.loss, cnn.accuracy],
                feed_dict)
            timestr = datetime.datetime.now().isoformat()
            print('{}: 步骤 {}, 损失 {:g}, 准确率 {:g}'.format(timestr, step, loss, accuracy))
            if writer:
                writer.add_summary(summaries, step)

        ### 训练循环 ###
        # 初始化所有变量
        sess.run(tf.global_variables_initializer())

        # 创建批次迭代器
        batches = data_helper.batch_iter(
            batch_size=FLAGS.batch_size,
            num_epochs=FLAGS.num_epochs,
            img_path_list=x_path_train,
            label_list=y_train,
            img_height=FLAGS.img_height,
            img_width=FLAGS.img_width)

        # 训练循环，处理每个批次
        for x_batch, y_batch in batches:
            train_step(x_batch, y_batch, writer=train_summary_writer)
            current_step = tf.train.global_step(sess, global_step)

            # 定期在验证集上评估
            if current_step % FLAGS.evaluate_every == 0:
                print('\n在验证集上评估:')
                dev_step(x_dev, y_dev, writer=dev_summary_writer)
                print('')

            # 定期保存模型检查点
            if current_step % FLAGS.checkpoint_every == 0:
                path = saver.save(sess=sess, save_path=checkpoint_prefix, global_step=global_step)
                print('\n模型检查点已保存到: {}\n'.format(path))

# 训练结束
print('\n--- 训练完成! ---\n')
