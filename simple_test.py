#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化的测试脚本 - 不依赖TensorFlow
用于验证基本环境和数据处理功能
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix, classification_report
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def check_dependencies():
    """检查依赖库"""
    print("=" * 50)
    print("环境依赖检查")
    print("=" * 50)
    
    required_packages = {
        'numpy': 'numpy',
        'matplotlib': 'matplotlib', 
        'sklearn': 'scikit-learn',
        'seaborn': 'seaborn'
    }
    
    missing_packages = []
    
    for package, install_name in required_packages.items():
        try:
            __import__(package)
            print(f"✓ {install_name}")
        except ImportError:
            print(f"✗ {install_name} - 需要安装")
            missing_packages.append(install_name)
    
    # 检查可选依赖
    optional_packages = {
        'cv2': 'opencv-python',
        'tensorflow': 'tensorflow'
    }
    
    print("\n可选依赖（用于完整功能）:")
    for package, install_name in optional_packages.items():
        try:
            __import__(package)
            print(f"✓ {install_name}")
        except ImportError:
            print(f"✗ {install_name} - 可选安装")
    
    if missing_packages:
        print(f"\n请安装缺失的依赖库:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def create_sample_data():
    """创建示例数据"""
    print("\n" + "=" * 50)
    print("创建示例数据")
    print("=" * 50)
    
    # 创建目录结构
    os.makedirs('./test_data/train/cat', exist_ok=True)
    os.makedirs('./test_data/train/dog', exist_ok=True)
    
    # 生成示例图像数据（不使用OpenCV）
    for i in range(10):
        # 猫的图像（偏蓝色模式）
        cat_img = np.random.randint(0, 100, (224, 224, 3), dtype=np.uint8)
        cat_img[:, :, 2] = np.random.randint(150, 255, (224, 224))  # 增强蓝色通道
        
        # 保存为numpy数组（模拟图像）
        np.save(f'./test_data/train/cat/cat.{i}.npy', cat_img)
        
        # 狗的图像（偏红色模式）
        dog_img = np.random.randint(0, 100, (224, 224, 3), dtype=np.uint8)
        dog_img[:, :, 0] = np.random.randint(150, 255, (224, 224))  # 增强红色通道
        
        np.save(f'./test_data/train/dog/dog.{i}.npy', dog_img)
    
    print("✓ 示例数据创建完成")
    print("  - 猫图像: 10张")
    print("  - 狗图像: 10张")

def test_data_processing():
    """测试数据处理功能"""
    print("\n" + "=" * 50)
    print("测试数据处理功能")
    print("=" * 50)
    
    try:
        # 模拟数据加载
        cat_files = [f'./test_data/train/cat/cat.{i}.npy' for i in range(10)]
        dog_files = [f'./test_data/train/dog/dog.{i}.npy' for i in range(10)]
        
        # 创建标签
        cat_labels = [[1, 0] for _ in range(10)]  # 猫: [1, 0]
        dog_labels = [[0, 1] for _ in range(10)]  # 狗: [0, 1]
        
        all_files = cat_files + dog_files
        all_labels = cat_labels + dog_labels
        
        print(f"✓ 数据文件数量: {len(all_files)}")
        print(f"✓ 标签数量: {len(all_labels)}")
        print(f"✓ 猫标签示例: {cat_labels[0]}")
        print(f"✓ 狗标签示例: {dog_labels[0]}")
        
        # 测试图像预处理
        sample_img = np.load(cat_files[0])
        print(f"✓ 原始图像形状: {sample_img.shape}")
        
        # 模拟灰度转换
        gray_img = np.dot(sample_img[...,:3], [0.299, 0.587, 0.114])
        gray_img = gray_img / 255.0
        print(f"✓ 灰度图像形状: {gray_img.shape}")
        print(f"✓ 像素值范围: [{gray_img.min():.3f}, {gray_img.max():.3f}]")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据处理测试失败: {e}")
        return False

def visualize_sample_data():
    """可视化示例数据"""
    print("\n" + "=" * 50)
    print("数据可视化测试")
    print("=" * 50)
    
    try:
        # 加载示例图像
        cat_img = np.load('./test_data/train/cat/cat.0.npy')
        dog_img = np.load('./test_data/train/dog/dog.0.npy')
        
        # 创建图表
        fig, axes = plt.subplots(1, 2, figsize=(10, 4))
        
        axes[0].imshow(cat_img)
        axes[0].set_title('示例猫图像')
        axes[0].axis('off')
        
        axes[1].imshow(dog_img)
        axes[1].set_title('示例狗图像')
        axes[1].axis('off')
        
        plt.tight_layout()
        plt.savefig('sample_data_visualization.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✓ 数据可视化完成，保存为 sample_data_visualization.png")
        return True
        
    except Exception as e:
        print(f"✗ 数据可视化失败: {e}")
        return False

def simulate_training_results():
    """模拟训练结果"""
    print("\n" + "=" * 50)
    print("模拟训练结果")
    print("=" * 50)
    
    try:
        # 模拟训练历史数据
        epochs = range(1, 51)
        
        # 自定义CNN模拟结果
        custom_train_acc = [0.5 + 0.33 * (1 - np.exp(-i/20)) + np.random.normal(0, 0.02) for i in epochs]
        custom_val_acc = [0.5 + 0.30 * (1 - np.exp(-i/20)) + np.random.normal(0, 0.03) for i in epochs]
        
        # VGG16迁移学习模拟结果
        vgg_train_acc = [0.7 + 0.28 * (1 - np.exp(-i/10)) + np.random.normal(0, 0.01) for i in epochs]
        vgg_val_acc = [0.7 + 0.25 * (1 - np.exp(-i/10)) + np.random.normal(0, 0.02) for i in epochs]
        
        # 绘制训练曲线
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        # 自定义CNN结果
        ax1.plot(epochs, custom_train_acc, 'b-', label='训练准确率', linewidth=2)
        ax1.plot(epochs, custom_val_acc, 'b--', label='验证准确率', linewidth=2)
        ax1.set_title('自定义CNN模型训练结果')
        ax1.set_xlabel('训练轮数')
        ax1.set_ylabel('准确率')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0.4, 1.0)
        
        # VGG16迁移学习结果
        ax2.plot(epochs, vgg_train_acc, 'r-', label='训练准确率', linewidth=2)
        ax2.plot(epochs, vgg_val_acc, 'r--', label='验证准确率', linewidth=2)
        ax2.set_title('VGG16迁移学习模型训练结果')
        ax2.set_xlabel('训练轮数')
        ax2.set_ylabel('准确率')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0.4, 1.0)
        
        plt.tight_layout()
        plt.savefig('training_results_comparison.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✓ 训练结果可视化完成，保存为 training_results_comparison.png")
        
        # 打印最终结果
        print(f"\n模拟的最终结果:")
        print(f"自定义CNN - 训练准确率: {custom_train_acc[-1]:.3f}, 验证准确率: {custom_val_acc[-1]:.3f}")
        print(f"VGG16迁移学习 - 训练准确率: {vgg_train_acc[-1]:.3f}, 验证准确率: {vgg_val_acc[-1]:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 训练结果模拟失败: {e}")
        return False

def create_confusion_matrix():
    """创建混淆矩阵示例"""
    print("\n" + "=" * 50)
    print("混淆矩阵示例")
    print("=" * 50)
    
    try:
        # 模拟预测结果
        # 自定义CNN混淆矩阵 (83%准确率)
        custom_cm = np.array([[420, 80], [90, 410]])
        
        # VGG16迁移学习混淆矩阵 (96%准确率)
        vgg_cm = np.array([[485, 15], [25, 475]])
        
        # 绘制混淆矩阵
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 自定义CNN混淆矩阵
        sns.heatmap(custom_cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['猫', '狗'], yticklabels=['猫', '狗'], ax=ax1)
        ax1.set_title('自定义CNN模型混淆矩阵')
        ax1.set_xlabel('预测标签')
        ax1.set_ylabel('真实标签')
        
        # VGG16混淆矩阵
        sns.heatmap(vgg_cm, annot=True, fmt='d', cmap='Greens',
                   xticklabels=['猫', '狗'], yticklabels=['猫', '狗'], ax=ax2)
        ax2.set_title('VGG16迁移学习模型混淆矩阵')
        ax2.set_xlabel('预测标签')
        ax2.set_ylabel('真实标签')
        
        plt.tight_layout()
        plt.savefig('confusion_matrices.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✓ 混淆矩阵可视化完成，保存为 confusion_matrices.png")
        
        # 计算性能指标
        def calculate_metrics(cm):
            tn, fp, fn, tp = cm.ravel()
            accuracy = (tp + tn) / (tp + tn + fp + fn)
            precision = tp / (tp + fp)
            recall = tp / (tp + fn)
            f1 = 2 * (precision * recall) / (precision + recall)
            return accuracy, precision, recall, f1
        
        custom_metrics = calculate_metrics(custom_cm)
        vgg_metrics = calculate_metrics(vgg_cm)
        
        print(f"\n性能指标对比:")
        print(f"自定义CNN - 准确率: {custom_metrics[0]:.3f}, 精确率: {custom_metrics[1]:.3f}, 召回率: {custom_metrics[2]:.3f}, F1: {custom_metrics[3]:.3f}")
        print(f"VGG16迁移学习 - 准确率: {vgg_metrics[0]:.3f}, 精确率: {vgg_metrics[1]:.3f}, 召回率: {vgg_metrics[2]:.3f}, F1: {vgg_metrics[3]:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 混淆矩阵创建失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("\n" + "=" * 50)
    print("清理测试数据")
    print("=" * 50)
    
    try:
        import shutil
        if os.path.exists('./test_data'):
            shutil.rmtree('./test_data')
            print("✓ 测试数据清理完成")
    except Exception as e:
        print(f"✗ 清理测试数据失败: {e}")

def main():
    """主函数"""
    print("基于CNN的猫狗图像分类 - 简化测试脚本")
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先安装必需的依赖库后再运行测试")
        return
    
    # 运行测试
    success_count = 0
    total_tests = 5
    
    create_sample_data()
    
    if test_data_processing():
        success_count += 1
    
    if visualize_sample_data():
        success_count += 1
        
    if simulate_training_results():
        success_count += 1
        
    if create_confusion_matrix():
        success_count += 1
        
    cleanup_test_data()
    success_count += 1
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"完成测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("✓ 所有基础功能测试通过！")
    else:
        print(f"⚠ 部分测试失败，请检查环境配置")
    
    print("\n生成的文件:")
    files = ['sample_data_visualization.png', 'training_results_comparison.png', 'confusion_matrices.png']
    for file in files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file}")
    
    print("\n下一步建议:")
    print("1. 安装完整依赖: pip install tensorflow opencv-python")
    print("2. 下载Kaggle猫狗数据集")
    print("3. 运行完整的训练脚本")

if __name__ == "__main__":
    main()
