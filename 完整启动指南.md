# 基于CNN的猫狗图像分类 - 完整启动指南

## 🎯 项目目标
使用Python 3.9环境，运行真实的CNN训练，而不是演示版本。

## 📋 完整步骤清单

### 步骤1: 环境配置 ✅

您已经切换到Python 3.9，现在安装依赖：

```bash
# 1. 检查Python版本
python --version  # 应该显示Python 3.9.x

# 2. 升级pip
python -m pip install --upgrade pip

# 3. 安装TensorFlow和相关依赖
pip install tensorflow==2.13.0
pip install keras==2.13.1
pip install opencv-python==********
pip install numpy==1.24.3
pip install matplotlib==3.7.2
pip install scikit-learn==1.3.0
pip install seaborn==0.12.2
pip install h5py==3.9.0
pip install python-gflags==3.1.2

# 4. 验证安装
python verify_environment.py
```

### 步骤2: 数据集准备 📁

#### 2.1 下载数据集
1. 访问 https://www.kaggle.com/c/dogs-vs-cats/data
2. 注册Kaggle账号（如果没有）
3. 下载 `train.zip` 文件（约543MB）
4. 解压到项目目录：
   ```bash
   unzip train.zip
   ```

#### 2.2 组织数据结构
```bash
# 运行数据组织脚本
python organize_dataset.py
```

脚本会自动将数据组织成以下结构：
```
cnn-classification-dog-vs-cat-master/
└── inputs/
    ├── train/
    │   ├── cat/     # 约11,875张猫图像
    │   └── dog/     # 约11,875张狗图像
    └── dev/
        ├── cat/     # 约625张猫图像
        └── dog/     # 约625张狗图像
```

### 步骤3: 运行训练 🚀

#### 3.1 自定义CNN训练
```bash
cd cnn-classification-dog-vs-cat-master
python train.py
```

**预期结果：**
- 训练时间：2-4小时（CPU）或30-60分钟（GPU）
- 最终准确率：约83%
- 模型保存在：`./log/[timestamp]/checkpoints/`

#### 3.2 VGG16迁移学习训练
```bash
cd cnn-classification-dog-vs-cat-master
python pre_train.py
```

**预期结果：**
- 训练时间：1-2小时（CPU）或15-30分钟（GPU）
- 最终准确率：约95%
- 模型保存在：`./log/VGG16-transfer-learning.model`

### 步骤4: 监控训练过程 📊

#### 4.1 使用TensorBoard（自定义CNN）
```bash
# 在新的终端窗口中运行
tensorboard --logdir ./log/

# 在浏览器中访问
http://localhost:6006
```

#### 4.2 查看训练输出
训练过程中会显示：
- 每个步骤的损失和准确率
- 每100步的验证结果
- 模型检查点保存信息

### 步骤5: 验证结果 ✅

#### 5.1 检查生成的文件
```bash
# 检查自定义CNN的日志目录
ls -la ./log/

# 检查VGG16模型文件
ls -la ./log/VGG16-transfer-learning.model
```

#### 5.2 查看训练历史
- TensorBoard图表（自定义CNN）
- Matplotlib图表（VGG16迁移学习）

## 🛠️ 故障排除

### 问题1: TensorFlow安装失败
```bash
# 解决方案：使用conda安装
conda install tensorflow=2.13.0 -c conda-forge
```

### 问题2: 内存不足
```bash
# 解决方案：减小批次大小
python train.py --batch_size 16
python pre_train.py --batch_size 16
```

### 问题3: GPU不可用
```bash
# 检查GPU状态
python -c "import tensorflow as tf; print(tf.config.list_physical_devices('GPU'))"

# 如果没有GPU，使用CPU训练（时间会更长）
```

### 问题4: 数据路径错误
```bash
# 检查数据目录结构
find ./cnn-classification-dog-vs-cat-master/inputs -type d

# 重新运行数据组织脚本
python organize_dataset.py
```

## 📈 预期训练输出示例

### 自定义CNN训练输出：
```
参数配置:
================================
train_data_dir: ./inputs/train/
img_height: 224
img_width: 224
batch_size: 32
num_epochs: 200
learning_rate: 0.001
================================

正在加载数据...
训练集大小: 23750
验证集大小: 237

开始训练模型...

2024-01-01 10:00:00: 步骤 1, 损失 0.693, 准确率 0.500
2024-01-01 10:01:00: 步骤 100, 损失 0.421, 准确率 0.781

在验证集上评估:
2024-01-01 10:01:30: 步骤 100, 损失 0.445, 准确率 0.759

模型检查点已保存到: ./log/[timestamp]/checkpoints/model-100
```

### VGG16迁移学习输出：
```
参数配置:
================================
train_data_dir: ./inputs/train/
dev_data_dir: ./inputs/dev/
img_height: 224
img_width: 224
batch_size: 32
num_epochs: 10
================================

正在加载VGG16预训练模型...
Found 23750 images belonging to 2 classes.
Found 1250 images belonging to 2 classes.

开始训练模型...
Epoch 1/10
742/742 [==============================] - 180s 243ms/step - loss: 0.1234 - accuracy: 0.9567 - val_loss: 0.0987 - val_accuracy: 0.9680

训练完成！
训练损失: 0.0234 / 验证损失: 0.0456
训练准确率: 99.12% / 验证准确率: 96.80%

模型已保存到: ./log/VGG16-transfer-learning.model
```

## 🎯 成功标志

当您看到以下内容时，说明训练成功：

1. ✅ **环境验证通过**：`verify_environment.py` 显示所有依赖正常
2. ✅ **数据组织完成**：`organize_dataset.py` 成功创建目录结构
3. ✅ **训练开始**：看到训练步骤和损失/准确率输出
4. ✅ **模型保存**：训练完成后生成模型文件
5. ✅ **结果可视化**：TensorBoard或matplotlib图表正常显示

## 🚀 下一步操作

训练完成后：

1. **分析结果**：比较两种模型的性能
2. **生成图表**：运行 `python demo_without_tensorflow.py` 生成论文图表
3. **完善论文**：将训练结果插入到论文中
4. **提交作业**：打包所有文件提交

## 📞 获取帮助

如果遇到问题：
1. 检查错误信息
2. 参考故障排除部分
3. 确认环境和数据准备是否正确
4. 查看训练输出日志

---

**准备好开始了吗？让我们从步骤1开始！** 🎓
