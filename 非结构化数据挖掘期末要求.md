一、考试内容

本次考试要求考生基于Anaconda环境，运用Python编程语言及PyCharm开发工具，结合pandas、numpy、sklearn和matplotlib等核心库，完成一个完整的非结构化数据挖掘项目。项目主题不限，但选题需符合非结构化数据挖掘的典型应用场景（如文本分析、图像识别、语音处理等），并体现完整的分析流程。考生需重点展示以下能力：1) 数据预处理（包括文本分词与清洗、图像归一化与增强、音频特征提取等）；2) 数据分析（运用统计方法和探索性分析揭示数据规律）；3) 模型构建与预测（使用sklearn实现机器学习算法，并进行模型评估）；4) 数据可视化（通过matplotlib等库直观呈现分析结果）。考核重点包括非结构化数据处理规范性、算法应用合理性、结果可视化效果，以及代码编写的规范性。特别要求掌握非结构化数据特征提取、数据挖掘模型的构建、模型性能评估等关键技术。

二、供选择题目：（每人选作其一或自定题目，自定题目的需征得老师同意）

1.垃圾邮件分类

题目：基于SMS垃圾邮件数据集的垃圾邮件分类

数据集：SMS Spam Collection Dataset

源码参考：

[https://github.com/ashishpatel26/Naive-Bayes-SMS-Spam-Collection](https://github.com/ashishpatel26/Naive-Bayes-SMS-Spam-Collection、)

[https://github.com/akshayk0406/SMS-SpamClassifier](https://github.com/akshayk0406/SMS-SpamClassifier)

2.BBC新闻数据分类

题目：基于BBC新闻数据集的新闻主题分类

数据集：BBC新闻数据

源码参考：[https://github.com/mattrea6/CMT316-article-classifier](https://github.com/mattrea6/CMT316-article-classifier)

3.社交媒体情感分析

题目：基于Sentiment140数据集的社交媒体情感分析与情感分类

数据集：Twitter Sentiment Analysis

源码参考：[https://github.com/johnksander/twitter_NLP_analysis](https://github.com/johnksander/twitter_NLP_analysis)

4.猫狗图像分类

题目：基于Kaggle Cats vs Dogs数据集的猫狗图像分类

数据集：Kaggle Cats vs Dogs

源码参考：[https://github.com/chenhui0518/cnn-classification-dog-vs-cat](https://github.com/chenhui0518/cnn-classification-dog-vs-cat)

5.人脸识别

题目：基于LFW小规模人脸数据集的人脸识别

数据集：Labelled Faces in the Wild (LFW) Dataset

源码参考：[https://github.com/zhangxd12/Lfw_face_recognition_svm_ensemble](https://github.com/zhangxd12/Lfw_face_recognition_svm_ensemble)

6.服装图像分类

题目：基于Fashion-MNIST服装图像的多类别分类

数据集：Fashion-MNIST

源码参考：[https://github.com/hsouri/Fashion-MNIST](https://github.com/hsouri/Fashion-MNIST)

7.社交媒体文本聚类与分析

题目：基于Twitter数据集的社交媒体文本聚类与话题分析

数据集：Twitter Sentiment Analysis

源码参考：[https://github.com/SakshiKasture/Clustering-Twitter-data](https://github.com/SakshiKasture/Clustering-Twitter-data)

8.中文文本聚类

题目：基于中文数据集的文本聚类

数据集：多个新闻网站爬取的新闻，包含教育类、游戏类、医疗类、体育类。

源码参考：[https://github.com/FesonX/cn-text-classifier](https://github.com/FesonX/cn-text-classifier)

9.水果分类

题目：基于FIDS30数据集的多类别水果分类

数据集：FIDS30 - Fruit Image Data Set

源码参考：[https://github.com/BiDu201/SVM_ClassiferFruits](https://github.com/BiDu201/SVM_ClassiferFruits)

10.音频分类

题目：基于GTZAN音乐数据集的音乐流派分类

数据集：GTZAN Music Genre

源码参考：[https://github.com/ryansingman/music-genre-classifier](https://github.com/ryansingman/music-genre-classifier)

三、要求：

1、项目选题要求创意新颖、立意独特，鼓励从实际生活、行业场景或前沿技术中提取灵感，需确保为学生本人独立完成的原创作品，严禁抄袭或套用他人项目成果。

2、项目难度与工作量需控制在合理范围内，选题应具有一定的数据处理与建模深度，体现学生对非结构化数据挖掘技术的理解与应用能力；所选数据集需来源明确、结构合理，数据量应具有分析意义，不能过于简单或样本过少。

3、提交的程序代码应逻辑清晰、结构规范，具备良好的可读性与可维护性；变量命名应语义明确，代码中建议适当添加注释说明，体现良好的编程习惯。

4、项目应完整体现数据挖掘的一般流程，包括但不限于：需求分析（明确研究问题与目标）、数据获取（从公开平台或自建渠道收集原始数据）、数据预处理（如清洗、转换、降维等操作）、数据挖掘建模（应用分类、聚类、关联规则、情感分析等算法），以及数据可视化（使用图表直观展现分析结果与发现）。

5、提交的小论文需按照学校毕业设计或课程设计的正式要求撰写，内容完整、格式规范，排版整齐，图表清晰，语言表达准确，具备较强的技术总结与分析能力。

6、论文内容应包括：研究课题的背景与意义、数据挖掘相关技术或算法介绍、项目实现的主要过程与关键步骤、实验设计与操作说明、结果展示与分析讨论、项目总结与展望等核心要素。

7、提交内容包括：完整的源代码文件、项目小论文各一份，统一按照“班级+学号+姓名”命名后压缩打包提交。