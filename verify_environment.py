#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
环境验证脚本
检查所有必需的依赖库是否正确安装
"""

import sys
import importlib

def check_python_version():
    """检查Python版本"""
    print("=" * 50)
    print("Python环境检查")
    print("=" * 50)
    
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 7:
        print("✓ Python版本兼容")
        return True
    else:
        print("✗ Python版本过低，建议使用Python 3.7+")
        return False

def check_package(package_name, import_name=None, min_version=None):
    """检查单个包的安装情况"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        
        # 检查版本
        if hasattr(module, '__version__'):
            version = module.__version__
            print(f"✓ {package_name}: {version}")
            
            if min_version and version < min_version:
                print(f"  ⚠ 建议版本: {min_version}+")
        else:
            print(f"✓ {package_name}: 已安装")
        
        return True
        
    except ImportError as e:
        print(f"✗ {package_name}: 未安装")
        print(f"  安装命令: pip install {package_name}")
        return False

def check_tensorflow():
    """特别检查TensorFlow"""
    print("\n" + "=" * 50)
    print("TensorFlow详细检查")
    print("=" * 50)
    
    try:
        import tensorflow as tf
        print(f"✓ TensorFlow版本: {tf.__version__}")
        
        # 检查GPU支持
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"✓ 检测到GPU: {len(gpus)}个")
            for i, gpu in enumerate(gpus):
                print(f"  GPU {i}: {gpu.name}")
        else:
            print("ℹ 未检测到GPU，将使用CPU训练")
        
        # 测试基本操作
        hello = tf.constant('Hello, TensorFlow!')
        print(f"✓ TensorFlow基本操作测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ TensorFlow测试失败: {e}")
        return False

def check_keras():
    """检查Keras"""
    try:
        import keras
        print(f"✓ Keras版本: {keras.__version__}")
        
        # 测试VGG16模型加载
        from keras.applications import VGG16
        print("✓ VGG16模型可以正常加载")
        
        return True
        
    except Exception as e:
        print(f"✗ Keras测试失败: {e}")
        return False

def main():
    """主检查函数"""
    print("基于CNN的猫狗图像分类项目 - 环境验证")
    
    # 检查Python版本
    python_ok = check_python_version()
    
    # 检查基础包
    print("\n" + "=" * 50)
    print("基础依赖库检查")
    print("=" * 50)
    
    packages = [
        ('numpy', 'numpy', '1.19.0'),
        ('matplotlib', 'matplotlib', '3.3.0'),
        ('opencv-python', 'cv2', '4.5.0'),
        ('scikit-learn', 'sklearn', '0.24.0'),
        ('seaborn', 'seaborn', '0.11.0'),
        ('h5py', 'h5py', '3.1.0'),
        ('python-gflags', 'gflags', '3.1.0'),
    ]
    
    basic_ok = True
    for package_name, import_name, min_version in packages:
        if not check_package(package_name, import_name, min_version):
            basic_ok = False
    
    # 检查TensorFlow
    tf_ok = check_tensorflow()
    
    # 检查Keras
    keras_ok = check_keras()
    
    # 总结
    print("\n" + "=" * 50)
    print("环境检查总结")
    print("=" * 50)
    
    if python_ok and basic_ok and tf_ok and keras_ok:
        print("🎉 所有依赖都已正确安装！")
        print("\n下一步操作:")
        print("1. 准备数据集")
        print("2. 运行训练脚本")
        print("   - 自定义CNN: python cnn-classification-dog-vs-cat-master/train.py")
        print("   - VGG16迁移学习: python cnn-classification-dog-vs-cat-master/pre_train.py")
        return True
    else:
        print("❌ 环境配置不完整")
        print("\n需要解决的问题:")
        if not python_ok:
            print("- Python版本需要升级")
        if not basic_ok:
            print("- 基础依赖库需要安装")
        if not tf_ok:
            print("- TensorFlow需要重新安装")
        if not keras_ok:
            print("- Keras需要重新安装")
        
        print("\n建议执行:")
        print("pip install tensorflow==2.13.0 keras==2.13.1 opencv-python numpy matplotlib scikit-learn seaborn h5py python-gflags")
        return False

if __name__ == "__main__":
    main()
